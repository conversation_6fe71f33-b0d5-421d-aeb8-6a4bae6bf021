from flask import request
from flask_restful import Resource, reqparse
from sqlalchemy import func

from controllers.console import api
from controllers.console.setup import setup_required
from controllers.console.wraps import account_initialization_required, login_required
from libs.login import current_user
from models.model import App, MessageFeedback, db


class AppFeedbackStatsApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """Get feedback statistics for apps"""
        parser = reqparse.RequestParser()
        parser.add_argument('app_ids', type=str, location='args', required=False)
        args = parser.parse_args()
        
        # Get app IDs from query parameter
        app_ids = []
        if args.get('app_ids'):
            app_ids = args['app_ids'].split(',')
        
        # If no specific app IDs provided, get all apps for current user's tenant
        if not app_ids:
            apps = db.session.query(App).filter(
                App.tenant_id == current_user.current_tenant_id,
                App.is_universal == False
            ).all()
            app_ids = [str(app.id) for app in apps]
        
        # Query feedback statistics
        stats_query = db.session.query(
            MessageFeedback.app_id,
            MessageFeedback.rating,
            func.count(MessageFeedback.id).label('count')
        ).filter(
            MessageFeedback.app_id.in_(app_ids),
            MessageFeedback.from_source == 'user'  # Only count user feedback
        ).group_by(
            MessageFeedback.app_id,
            MessageFeedback.rating
        ).all()
        
        # Organize results by app_id
        feedback_stats = {}
        for app_id in app_ids:
            feedback_stats[app_id] = {
                'like': 0,
                'dislike': 0,
                'total': 0,
                'satisfaction_rate': 0.0
            }
        
        # Fill in the actual counts
        for stat in stats_query:
            app_id = str(stat.app_id)
            if app_id in feedback_stats:
                if stat.rating == 'like':
                    feedback_stats[app_id]['like'] = stat.count
                elif stat.rating == 'dislike':
                    feedback_stats[app_id]['dislike'] = stat.count
        
        # Calculate totals and satisfaction rates
        for app_id in feedback_stats:
            stats = feedback_stats[app_id]
            stats['total'] = stats['like'] + stats['dislike']
            if stats['total'] > 0:
                stats['satisfaction_rate'] = round(stats['like'] / stats['total'] * 100, 1)
        
        return {
            'result': 'success',
            'data': feedback_stats
        }


class AppDetailedFeedbackStatsApi(Resource):
    @setup_required
    @login_required
    @account_initialization_required
    def get(self, app_id):
        """Get detailed feedback statistics for a specific app"""
        
        # Verify app belongs to current user's tenant
        app = db.session.query(App).filter(
            App.id == app_id,
            App.tenant_id == current_user.current_tenant_id
        ).first()
        
        if not app:
            return {'message': 'App not found'}, 404
        
        # Get basic stats
        basic_stats = db.session.query(
            MessageFeedback.rating,
            func.count(MessageFeedback.id).label('count')
        ).filter(
            MessageFeedback.app_id == app_id,
            MessageFeedback.from_source == 'user'
        ).group_by(MessageFeedback.rating).all()
        
        # Get problem type breakdown from dislike feedback
        problem_stats = db.session.query(
            MessageFeedback.content,
            func.count(MessageFeedback.id).label('count')
        ).filter(
            MessageFeedback.app_id == app_id,
            MessageFeedback.from_source == 'user',
            MessageFeedback.rating == 'dislike',
            MessageFeedback.content.isnot(None)
        ).group_by(MessageFeedback.content).all()
        
        # Process basic stats
        stats = {
            'like': 0,
            'dislike': 0,
            'total': 0,
            'satisfaction_rate': 0.0
        }
        
        for stat in basic_stats:
            if stat.rating == 'like':
                stats['like'] = stat.count
            elif stat.rating == 'dislike':
                stats['dislike'] = stat.count
        
        stats['total'] = stats['like'] + stats['dislike']
        if stats['total'] > 0:
            stats['satisfaction_rate'] = round(stats['like'] / stats['total'] * 100, 1)
        
        # Process problem types
        problem_breakdown = {
            'inaccurate': 0,
            'slow': 0,
            'irrelevant': 0,
            'incomplete': 0,
            'other': 0
        }
        
        for problem in problem_stats:
            content = problem.content or ''
            if content.startswith('inaccurate:'):
                problem_breakdown['inaccurate'] += problem.count
            elif content.startswith('slow:'):
                problem_breakdown['slow'] += problem.count
            elif content.startswith('irrelevant:'):
                problem_breakdown['irrelevant'] += problem.count
            elif content.startswith('incomplete:'):
                problem_breakdown['incomplete'] += problem.count
            elif content.startswith('other:'):
                problem_breakdown['other'] += problem.count
            else:
                problem_breakdown['other'] += problem.count
        
        # Get recent feedback (last 10)
        recent_feedback = db.session.query(MessageFeedback).filter(
            MessageFeedback.app_id == app_id,
            MessageFeedback.from_source == 'user'
        ).order_by(MessageFeedback.created_at.desc()).limit(10).all()
        
        recent_feedback_list = []
        for feedback in recent_feedback:
            recent_feedback_list.append({
                'id': str(feedback.id),
                'rating': feedback.rating,
                'content': feedback.content,
                'created_at': feedback.created_at.isoformat() if feedback.created_at else None
            })
        
        return {
            'result': 'success',
            'data': {
                'app_id': app_id,
                'app_name': app.name,
                'basic_stats': stats,
                'problem_breakdown': problem_breakdown,
                'recent_feedback': recent_feedback_list
            }
        }


api.add_resource(AppFeedbackStatsApi, '/apps/feedback-stats')
api.add_resource(AppDetailedFeedbackStatsApi, '/apps/<uuid:app_id>/feedback-stats')
